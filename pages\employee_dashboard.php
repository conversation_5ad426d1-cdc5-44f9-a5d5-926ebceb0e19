<?php
// employee_dashboard.php
include 'database/db_connection.php';
include '../includes/email_helper.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page if not logged in
    header("Location: login.php");
    exit;
}
?>

<!doctype html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="OJT Monitoring System is a platform designed to track and manage on-the-job training programs efficiently.">
    <meta name="author" content="Your Name or Organization">

    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" />
    <link rel="stylesheet" href="../assets/MDB5-STANDARD-UI-KIT/css/mdb.min.css" />
    <link rel="stylesheet" href="../assets/boxicons-master/css/boxicons.min.css">
    <script src="../assets/js/JsBarcode.all.min.js"></script>

    <title>OJT Monitoring System</title>
    <?php include '../assets/css/employee_profile.php'; ?>
    <script>
        // Pass the logged-in user's profile image to JavaScript
        // For employee_dashboard.php, we want to use the logged-in user's profile image
        var phpSessionProfileImage = <?php echo isset($_SESSION['profile_image']) ? json_encode($_SESSION['profile_image']) : 'null'; ?>;

        // Pass the course from session if available
        var phpSessionCourse = <?php echo isset($_SESSION['course']) ? json_encode($_SESSION['course']) : 'null'; ?>;
    </script>
    <style>
        /* Custom styles for journal content list */
        .content-list {
            list-style-type: none;
            padding-left: 0;
            margin-top: 10px;
        }

        .content-list li {
            margin-bottom: 8px;
            padding-left: 80px; /* Double tab indentation */
            position: relative;
            text-indent: 0;
        }

        .content-list .bullet {
            position: absolute;
            left: 60px; /* Position for double tab */
            font-size: 18px;
            line-height: 1;
        }

        /* Custom styles for content fields */
        .content-field {
            margin-bottom: 10px;
        }

        .content-field .content-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .content-field .remove-content-btn {
            height: fit-content;
            align-self: flex-start;
        }

        /* Custom styles for overtime status badges */
        .badge {
            padding: 6px 10px;
            font-size: 0.8rem;
            font-weight: 500;
            border-radius: 4px;
            display: inline-block;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
        }
        .bg-success {
            background-color: #28a745 !important;
            color: white;
        }
        .bg-danger {
            background-color: #dc3545 !important;
            color: white;
        }

        /* Loading state styles */
        .stat-card.loading {
            opacity: 0.7;
        }

        .loading-text {
            display: inline-block;
            position: relative;
            min-width: 60px;
        }

        .loading-text:after {
            content: "...";
            position: absolute;
            width: 0;
            right: -12px;
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {
            0% { width: 0; }
            33% { width: 4px; }
            66% { width: 8px; }
            100% { width: 12px; }
        }

        /* Custom styles for undertime card */
        .undertime-card::before {
            background: linear-gradient(90deg, #9c27b0, #7b1fa2) !important;
        }

        .undertime-icon {
            background: linear-gradient(135deg, #9c27b0, #7b1fa2) !important;
        }
    </style>
</head>


<body>
    <main>
        <div class="page-header">
            <div class="container py-4">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="logo-container">
                            <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="img-fluid logo-image">
                        </div>
                    </div>
                    <div class="col">
                        <div class="header-info">
                            <h3 class="header-title">DICT Surigao del Norte Provincial Office</h3>
                            <p class="header-address">Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p>
                        </div>
                    </div>
                    <div class="col-auto">
                        <?php
                        // Check if user is logged in and has a role
                        if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'Admin') {
                            // Only show back button for Admin users
                            // Set default back URL
                            $back_url = 'manage_employee.php'; // Default fallback

                            // Use JavaScript history.back() instead of a fixed URL
                            // This will use the browser's history to go back to the previous page
                            $use_history = true;

                            // However, if we came directly to this page (no referrer), use the default URL
                            if (!isset($_SERVER['HTTP_REFERER'])) {
                                $use_history = false;
                            } else {
                                // Check if referrer is from the same domain
                                $referer = $_SERVER['HTTP_REFERER'];
                                $host = $_SERVER['HTTP_HOST'];

                                // If referrer is not from the same domain, use the default URL
                                if (strpos($referer, $host) === false) {
                                    $use_history = false;
                                }
                            }

                            if ($use_history): ?>
                            <a href="javascript:history.back();" class="return-button">
                                <span class="return-icon"><i class="fa fa-arrow-left"></i></span>
                                <span class="return-text">Back</span>
                            </a>
                            <?php else: ?>
                            <a href="<?php echo htmlspecialchars($back_url); ?>" class="return-button">
                                <span class="return-icon"><i class="fa fa-arrow-left"></i></span>
                                <span class="return-text">Back</span>
                            </a>
                            <?php endif;
                        } else {
                            // For Employee users, show dropdown menu instead of back button
                            ?>
                            <div class="dropdown">
                                <button class="btn btn-light text-dark" type="button" id="userMenuDropdown" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false" style="background-color: #f8f9fa; border: 1px solid #dee2e6;">
                                    <?php echo isset($_SESSION['user_name']) ? htmlspecialchars($_SESSION['user_name']) : 'Employee'; ?> <i class="fas fa-caret-down ms-1"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="userMenuDropdown">
                                    <li><a class="dropdown-item" href="employee_profile.php"><i class="fas fa-user me-2"></i>Profile Page</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" id="logoutBtn"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                                </ul>
                            </div>
                        <?php } ?>
                    </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid flex-grow-1 pt-4">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="profile-header mb-5">
                            <h2 class="text-center position-relative mb-4">
                                <span class="profile-title">Employee Profile</span>
                                <div class="title-underline"></div>
                            </h2>
                        </div>

                        <div id="employee-profile" class="profile-card mb-4">
                            <div class="profile-card-inner row">
                                <div class="col-md-4 profile-image-section">
                                    <div id="profile-image-container" class="profile-image-wrapper">
                                        <div class="profile-image-border">
                                            <img id="profile-image" src="" alt="Profile Image" class="img-fluid">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8 profile-details-section">
                                    <div class="profile-details">
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Employee ID</div>
                                            <div class="detail-value" id="employee-id"></div>
                                        </div>
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Full Name</div>
                                            <div class="detail-value" id="full-name"></div>
                                        </div>
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Course</div>
                                            <div class="detail-value" id="course"></div>
                                        </div>
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Department</div>
                                            <div class="detail-value" id="department"></div>
                                        </div>
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Date Registered</div>
                                            <div class="detail-value" id="date-registered"></div>
                                        </div>
                                        <div class="profile-detail-item">
                                            <div class="detail-label">Date Started</div>
                                            <div class="detail-value" id="date-started"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr>

                        <div class="row">
                            <div class="section-header">
                                <h5 class="section-title">Details</h5>
                                <div class="section-underline"></div>
                                <br />
                            </div>

                            <!-- Filter Card - Similar to dtr_table.php -->
                            <div class="container-fluid mt-4 mb-4">
                                <div class="row g-3 align-items-end bg-white p-3 rounded border filter-card">
                                    <div class="col-md-5">
                                        <label for="detailsMonthFilter" class="form-label fw-bold">Month:</label>
                                        <select id="detailsMonthFilter" class="form-select form-select-sm" aria-label="Month Filter">
                                            <option value="all" selected>All</option>
                                            <option value="month1">January</option>
                                            <option value="month2">February</option>
                                            <option value="month3">March</option>
                                            <option value="month4">April</option>
                                            <option value="month5">May</option>
                                            <option value="month6">June</option>
                                            <option value="month7">July</option>
                                            <option value="month8">August</option>
                                            <option value="month9">September</option>
                                            <option value="month10">October</option>
                                            <option value="month11">November</option>
                                            <option value="month12">December</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="customDaysFilter" class="form-label fw-bold">Days:</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" id="customDaysFilter" class="form-control" min="1" placeholder="Enter days" aria-label="Custom Days Filter" value="">
                                        </div>
                                    </div>

                                    <!-- Duty Hours filter removed as we're using standard duty hours -->

                                    <div class="col-md-2">
                                        <button id="applyDaysFilter" class="btn btn-primary btn-sm w-100" type="button">
                                            <i class="fas fa-filter me-1"></i>Apply Filter
                                        </button>
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-info btn-sm w-100" data-mdb-modal-init data-mdb-target="#filteringGuideModal">
                                            <i class="fa fa-info-circle me-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="stats-container">
                                <div class="row">
                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card present-card" id="total_present_card">
                                            <div class="stat-icon">
                                                <div class="icon-circle present-icon">
                                                    <i class="fas fa-calendar-check"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_present_display">0</h3>
                                                <p>Total Present</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card absent-card" id="total_absent_card">
                                            <div class="stat-icon">
                                                <div class="icon-circle absent-icon">
                                                    <i class="fas fa-calendar-times"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_absent_display">0</h3>
                                                <p>Total Absent</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card hours-required-card">
                                            <div class="stat-icon">
                                                <div class="icon-circle hours-required-icon">
                                                    <i class="fas fa-hourglass-start"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_hours_display">0</h3>
                                                <p>Hours Required</p>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                                <div class="row">


                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card undertime-card" id="total_undertime_card">
                                            <div class="stat-icon">
                                                <div class="icon-circle undertime-icon">
                                                    <i class="fa-solid fa-clock"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_undertime_display">0</h3>
                                                <p>Total Undertime</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card hours-complete-card" id="total_hours_complete_card">
                                            <div class="stat-icon">
                                                <div class="icon-circle hours-complete-icon">
                                                    <i class="fas fa-hourglass-half"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_hours_complete_display">0</h3>
                                                <p>Hours Complete</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-lg-4 col-xl-4 mb-4">
                                        <div class="stat-card hours-remaining-card" id="total_hours_remaining_card">
                                            <div class="stat-icon">
                                                <div class="icon-circle hours-remaining-icon">
                                                    <i class="fas fa-hourglass-end"></i>
                                                </div>
                                            </div>
                                            <div class="stat-info">
                                                <h3 id="total_hours_remaining_display">0</h3>
                                                <p>Remaining Hours</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>


                        <div class="row">
                            <div class="section-header">
                                <h5 class="section-title">History</h5>
                                <div class="section-underline"></div>
                            </div>
                            <section>
                                <div class="row">
                                    <div class="custom-card">
                                        <div class="custom-card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-4">
                                                <div class="card-title-container">
                                                    <h2 class="custom-card-title">History Lists</h2>
                                                    <div class="card-title-underline"></div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Rows:</span>
                                                        <div class="btn-group">
                                                            <button id="rowsDropdownBtn" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                5
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item rows-option" href="#" data-value="5">5</a></li>
                                                                <li><a class="dropdown-item rows-option" href="#" data-value="10">10</a></li>
                                                                <li><a class="dropdown-item rows-option" href="#" data-value="25">25</a></li>
                                                                <li><a class="dropdown-item rows-option" href="#" data-value="100">100</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Month:</span>
                                                        <div class="btn-group">
                                                            <button id="historyMonthFilterBtn" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                All Months
                                                            </button>
                                                            <ul class="dropdown-menu month-dropdown-menu">
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="all">All Months</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="1">January</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="2">February</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="3">March</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="4">April</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="5">May</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="6">June</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="7">July</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="8">August</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="9">September</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="10">October</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="11">November</a></li>
                                                                <li><a class="dropdown-item history-month-option" href="#" data-value="12">December</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="search-container me-3">
                                                        <div class="search-input-wrapper">
                                                            <i class="fas fa-search search-icon"></i>
                                                            <input type="text" id="searchInputHistory" class="search-input" placeholder="Search..." aria-label="Search">
                                                        </div>
                                                    </div>
                                                    <button id="viewDtrBtn" class="action-button view-dtr-button me-2" type="button">
                                                        <i class="fas fa-calendar-alt me-2"></i> View DTR
                                                    </button>
                                                    <button id="printHistoryBtn" class="action-button print-history-button" type="button">
                                                        <i class="fas fa-print me-2"></i> Print History
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="custom-table-container">
                                                <table id="historyTable" class="custom-table history-table">
                                                    <thead>
                                                        <tr class="table-header-main">
                                                            <th rowspan="2" class="text-center">#</th>
                                                            <th class="text-center"></th>
                                                            <th colspan="2" class="text-center morning-header">Morning</th>
                                                            <th colspan="2" class="text-center afternoon-header">Afternoon</th>
                                                            <th colspan="3" class="text-center hours-header">Hours</th>
                                                            <th rowspan="2" class="text-center">Overtime</th>
                                                        </tr>
                                                        <tr class="table-header-sub">
                                                            <th class="text-center">Date</th>
                                                            <th class="text-center">Time In</th>
                                                            <th class="text-center">Time Out</th>
                                                            <th class="text-center">Time In</th>
                                                            <th class="text-center">Time Out</th>
                                                            <th class="text-center">Morning</th>
                                                            <th class="text-center">Afternoon</th>
                                                            <th class="text-center">Total</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="historyTableBody">
                                                        <tr>
                                                            <td colspan="10" class="text-center">
                                                                <div class="loading-indicator">
                                                                    <i class="fas fa-spinner fa-pulse"></i>
                                                                    <span>Loading data...</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <th scope="col" class="text-center">#</th>
                                                            <th scope="col" class="text-center">Date</th>
                                                            <th scope="col" class="text-center">Time In</th>
                                                            <th scope="col" class="text-center">Time Out</th>
                                                            <th scope="col" class="text-center">Time In</th>
                                                            <th scope="col" class="text-center">Time Out</th>
                                                            <th scope="col" class="text-center">Morning</th>
                                                            <th scope="col" class="text-center">Afternoon</th>
                                                            <th scope="col" class="text-center">Total</th>
                                                            <th scope="col" class="text-center">Overtime</th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                            <div class="custom-pagination" id="paginationHistory">
                                                <div class="pagination-info" id="pageInfoHistory">
                                                    <span class="pagination-text">Result of 1-5 of 0</span>
                                                </div>

                                                <div class="pagination-controls">
                                                    <button class="pagination-button prev-button disabled" id="prevPageHistory" aria-label="Previous">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </button>
                                                    <button class="pagination-button next-button disabled" id="nextPageHistory" aria-label="Next">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>


                        <!-- Daily Journal section commented out
                        <hr>
                        <div class="row">
                            <!-- Daily Journal section commented out
                            <div class="section-header">
                                <h5 class="section-title">Daily Journal</h5>
                                <div class="section-underline"></div>
                            </div>
                            <section>
                                <div class="row">
                                    <div class="custom-card">
                                        <div class="custom-card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-4">
                                                <div class="card-title-container">
                                                    <h2 class="custom-card-title">Journal Entries</h2>
                                                    <div class="card-title-underline"></div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Rows:</span>
                                                        <div class="btn-group">
                                                            <button id="rowsDropdownBtnJournal" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                5
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item rows-option-journal" href="#" data-value="5">5</a></li>
                                                                <li><a class="dropdown-item rows-option-journal" href="#" data-value="10">10</a></li>
                                                                <li><a class="dropdown-item rows-option-journal" href="#" data-value="25">25</a></li>
                                                                <li><a class="dropdown-item rows-option-journal" href="#" data-value="100">100</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Month:</span>
                                                        <div class="btn-group">
                                                            <button id="journalMonthFilterBtn" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                All Months
                                                            </button>
                                                            <ul class="dropdown-menu month-dropdown-menu">
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="all">All Months</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="1">January</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="2">February</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="3">March</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="4">April</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="5">May</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="6">June</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="7">July</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="8">August</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="9">September</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="10">October</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="11">November</a></li>
                                                                <li><a class="dropdown-item journal-month-option" href="#" data-value="12">December</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <div class="search-container me-3">
                                                        <div class="search-input-wrapper">
                                                            <i class="fas fa-search search-icon"></i>
                                                            <input type="text" id="searchInputJournal" class="search-input" placeholder="Search..." aria-label="Search">
                                                        </div>
                                                    </div>
                                                    <button id="printJournalBtn" class="action-button print-journal-button me-2" type="button">
                                                        <i class="fas fa-print me-2"></i> Print Journal
                                                    </button>
                                                    <button type="button" class="action-button add-journal-button" id="addJournalBtn">
                                                        <i class="fas fa-plus me-2"></i> Add Entry
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="custom-table-container">
                                                <table id="journalTable" class="custom-table">
                                                    <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center">#</th>
                                                            <th scope="col" class="text-center">Date</th>
                                                            <th scope="col" class="text-center">Title</th>
                                                            <th scope="col" class="text-center">Content</th>
                                                            <th scope="col" class="text-center">Created</th>
                                                            <th scope="col" class="text-center">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="journalTableBody">
                                                        <tr>
                                                            <td colspan="6" class="text-center">
                                                                <div class="loading-indicator">
                                                                    <i class="fas fa-spinner fa-pulse"></i>
                                                                    <span>Loading journal entries...</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <th scope="col" class="text-center">#</th>
                                                            <th scope="col" class="text-center">Date</th>
                                                            <th scope="col" class="text-center">Title</th>
                                                            <th scope="col" class="text-center">Content</th>
                                                            <th scope="col" class="text-center">Created</th>
                                                            <th scope="col" class="text-center">Actions</th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                            <div class="custom-pagination" id="paginationJournal">
                                                <div class="pagination-info" id="pageInfoJournal">
                                                    <span class="pagination-text">Result of 0-0 of 0</span>
                                                </div>

                                                <div class="pagination-controls">
                                                    <button class="pagination-button prev-button disabled" id="prevPageJournal" aria-label="Previous">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </button>
                                                    <button class="pagination-button next-button disabled" id="nextPageJournal" aria-label="Next">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>

                        <hr>
-->
                        <div class="row">
                            <div class="section-header">
                                <h5 class="section-title">Journal Documents</h5>
                                <div class="section-underline"></div>
                            </div>
                            <section>
                                <div class="row">
                                    <div class="custom-card">
                                        <div class="custom-card-body">
                                            <div class="d-flex justify-content-between align-items-center mb-4">
                                                <div class="card-title-container">
                                                    <h2 class="custom-card-title">Document Uploads</h2>
                                                    <div class="card-title-underline"></div>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Rows:</span>
                                                        <div class="btn-group">
                                                            <button id="rowsDropdownBtnDocs" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                5
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item rows-option-docs" href="#" data-value="5">5</a></li>
                                                                <li><a class="dropdown-item rows-option-docs" href="#" data-value="10">10</a></li>
                                                                <li><a class="dropdown-item rows-option-docs" href="#" data-value="25">25</a></li>
                                                                <li><a class="dropdown-item rows-option-docs" href="#" data-value="100">100</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modern-dropdown me-3">
                                                        <span class="dropdown-label">Month:</span>
                                                        <div class="btn-group">
                                                            <button id="docsMonthFilterBtn" class="btn-dropdown dropdown-toggle" type="button" data-mdb-dropdown-init data-mdb-ripple-init aria-expanded="false">
                                                                All Months
                                                            </button>
                                                            <ul class="dropdown-menu month-dropdown-menu">
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="all">All Months</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="1">January</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="2">February</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="3">March</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="4">April</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="5">May</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="6">June</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="7">July</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="8">August</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="9">September</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="10">October</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="11">November</a></li>
                                                                <li><a class="dropdown-item docs-month-option" href="#" data-value="12">December</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <div class="search-container me-3">
                                                        <div class="search-input-wrapper">
                                                            <i class="fas fa-search search-icon"></i>
                                                            <input type="text" id="searchInputDocs" class="search-input" placeholder="Search..." aria-label="Search">
                                                        </div>
                                                    </div>
                                                    <button type="button" class="action-button add-doc-button" id="addDocBtn">
                                                        <i class="fas fa-upload me-2"></i> Upload Document
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="custom-table-container">
                                                <table id="docsTable" class="custom-table">
                                                    <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center">#</th>
                                                            <th scope="col" class="text-center">Date</th>
                                                            <th scope="col" class="text-center">Title</th>
                                                            <th scope="col" class="text-center">File Type</th>
                                                            <th scope="col" class="text-center">Created</th>
                                                            <th scope="col" class="text-center">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="docsTableBody">
                                                        <tr>
                                                            <td colspan="6" class="text-center">
                                                                <div class="loading-indicator">
                                                                    <i class="fas fa-spinner fa-pulse"></i>
                                                                    <span>Loading documents...</span>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <tfoot>
                                                        <tr>
                                                            <th scope="col" class="text-center">#</th>
                                                            <th scope="col" class="text-center">Date</th>
                                                            <th scope="col" class="text-center">Title</th>
                                                            <th scope="col" class="text-center">File Type</th>
                                                            <th scope="col" class="text-center">Created</th>
                                                            <th scope="col" class="text-center">Actions</th>
                                                        </tr>
                                                    </tfoot>
                                                </table>
                                            </div>
                                            <div class="custom-pagination" id="paginationDocs">
                                                <div class="pagination-info" id="pageInfoDocs">
                                                    <span class="pagination-text">Result of 0-0 of 0</span>
                                                </div>

                                                <div class="pagination-controls">
                                                    <button class="pagination-button prev-button disabled" id="prevPageDocs" aria-label="Previous">
                                                        <i class="fas fa-chevron-left"></i>
                                                    </button>
                                                    <button class="pagination-button next-button disabled" id="nextPageDocs" aria-label="Next">
                                                        <i class="fas fa-chevron-right"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                </div>
            </div>
        </div>
        <footer class="modern-footer">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="../assets/img/dict_logo_icon.png" alt="Logo" class="footer-logo-img">
                </div>
                <div class="footer-info">
                    <p class="copyright">© 2025 OJT Monitoring System</p>
                    <p class="developer">Developed by: <a href="#" class="developer-link">John Lloyd Caban</a></p>
                </div>
            </div>
        </footer>
    </main>

    <!-- Filtering Guide Modal -->
    <div class="modal top fade" id="filteringGuideModal" tabindex="-1" aria-labelledby="filteringGuideModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filteringGuideModalLabel">Attendance Filtering Guide</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="filtering-guide">
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <strong>Month Filter:</strong> Select a specific month to filter attendance records, Present count, Absent count, and Hours Complete for that month only.
                            </li>
                            <li class="mb-3">
                                <strong>Days Filter:</strong> Enter a number (e.g., 5, 15, 30) to show statistics for the first X days of attendance, starting from the employee's earliest recorded attendance.
                            </li>

                            <li class="mb-3">
                                <strong>Duty Hours Filter:</strong> Select how to calculate working hours:
                                <ul class="ms-3 mt-2">
                                    <li><strong>All Hours:</strong> Calculates Total Hours Present as 8 hours for each day present (standard workday), regardless of actual hours worked</li>
                                    <li><strong>Count From Actual Time In:</strong> Calculates hours from actual time in to fixed time out (12:00 PM for morning and 5:00 PM for afternoon), capped at 8 hours per day</li>
                                </ul>
                            </li>

                            <li class="mb-3">
                                <strong>Combined Filtering:</strong> Use multiple filters together to see statistics for specific periods with custom hour calculations.
                            </li>
                            <li class="mb-3">
                                <strong>Statistics Impact:</strong> The filters will update the Total Present, Total Absent, and Hours Complete cards with filtered data.
                            </li>
                            <li class="mb-3">
                                <strong>Apply Filters:</strong> After making your selections, click the "Apply Filter" button to update the statistics display.
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>



    <!-- Journal Modals -->
    <!-- Add Journal Modal -->
    <div class="modal top fade" id="addJournalModal" tabindex="-1" aria-labelledby="addJournalModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addJournalModalLabel">Add Journal Entry</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addJournalForm">
                        <div class="mb-3">
                            <label for="entryDate" class="form-label">Date</label>
                            <input type="date" class="form-control" id="entryDate" name="entryDate" required value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="entryTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="entryTitle" name="entryTitle" required placeholder="Enter journal title">
                        </div>
                        <div class="mb-3">
                            <label for="entryContent" class="form-label">Content</label>
                            <textarea class="form-control" id="entryContent" name="entryContent" rows="6" required placeholder="Write your journal entry here..."></textarea>
                            <div class="text-end mt-2">
                                <button type="button" class="btn btn-sm btn-primary" id="addBulletPointBtn" onclick="addBulletPoint('entryContent'); return false;">
                                    <i class="fas fa-list-ul me-1"></i> Add Bullet Point
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveJournalBtn">Save Entry</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Journal Modal -->
    <div class="modal top fade" id="editJournalModal" tabindex="-1" aria-labelledby="editJournalModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editJournalModalLabel">Edit Journal Entry</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editJournalForm">
                        <input type="hidden" id="editJournalId" name="journalId">
                        <div class="mb-3">
                            <label for="editEntryDate" class="form-label">Date</label>
                            <input type="date" class="form-control" id="editEntryDate" name="entryDate" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEntryTitle" class="form-label">Title</label>
                            <input type="text" class="form-control" id="editEntryTitle" name="entryTitle" required placeholder="Enter journal title">
                        </div>
                        <div class="mb-3">
                            <label for="editEntryContent" class="form-label">Content</label>
                            <textarea class="form-control" id="editEntryContent" name="entryContent" rows="6" required placeholder="Write your journal entry here..."></textarea>
                            <div class="text-end mt-2">
                                <button type="button" class="btn btn-sm btn-primary" id="editAddBulletPointBtn" onclick="addBulletPoint('editEntryContent'); return false;">
                                    <i class="fas fa-list-ul me-1"></i> Add Bullet Point
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="updateJournalBtn">Update Entry</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Journal Modal -->
    <div class="modal top fade" id="viewJournalModal" tabindex="-1" aria-labelledby="viewJournalModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewJournalModalLabel">Journal Entry</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="journal-view-header mb-4">
                        <h4 id="viewJournalTitle" class="mb-2"></h4>
                        <div class="d-flex justify-content-between text-muted small">
                            <span id="viewJournalDate"></span>
                            <span id="viewJournalCreated"></span>
                        </div>
                    </div>
                    <div class="journal-view-content">
                        <div id="viewJournalContent" class="mb-0"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Document Modal -->
    <div class="modal top fade" id="uploadDocModal" tabindex="-1" aria-labelledby="uploadDocModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadDocModalLabel">Upload Journal Document</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadDocForm">
                        <div class="mb-3">
                            <label for="docDate" class="form-label">Date</label>
                            <input type="date" class="form-control" id="docDate" name="docDate" required value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="mb-3">
                            <label for="docTitle" class="form-label">Document Title</label>
                            <input type="text" class="form-control" id="docTitle" name="docTitle" required placeholder="Enter document title">
                        </div>
                        <div class="mb-3">
                            <label for="docFile" class="form-label">Document File</label>
                            <input type="file" class="form-control" id="docFile" name="docFile" accept=".doc,.docx,.pdf" required>
                            <div class="form-text">Allowed file types: DOC, DOCX, PDF (Max size: 10MB)</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveDocBtn">Upload Document</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Document Modal -->
    <div class="modal top fade" id="viewDocModal" tabindex="-1" aria-labelledby="viewDocModalLabel" aria-hidden="true" data-mdb-backdrop="true" data-mdb-keyboard="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewDocModalLabel">Document Details</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="doc-view-header mb-3">
                        <h4 id="viewDocTitle" class="mb-2"></h4>
                        <div class="d-flex justify-content-between text-muted small">
                            <span id="viewDocDate"></span>
                            <span id="viewDocCreated"></span>
                        </div>
                    </div>
                    <div class="doc-view-content mb-3">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <div class="doc-icon me-3">
                                <i id="docTypeIcon" class="fas fa-file-pdf fa-2x text-danger"></i>
                            </div>
                            <div class="doc-info flex-grow-1">
                                <p id="viewDocFilename" class="mb-1 fw-bold"></p>
                                <p id="viewDocFileType" class="mb-0 text-muted small"></p>
                            </div>
                            <a id="downloadDocBtn" href="#" class="btn btn-primary">
                                <i class="fas fa-download me-1"></i> Download
                            </a>
                        </div>
                    </div>

                    <div class="doc-preview-container mt-4">
                        <h5 class="mb-3">Document Preview</h5>
                        <div id="docPreviewLoading" class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading document preview...</p>
                        </div>
                        <div id="docPreviewError" class="alert alert-warning d-none">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="docPreviewErrorMessage">Preview not available for this document type.</span>
                        </div>
                        <div id="docPreviewContent" class="border rounded" style="height: 600px; overflow: hidden;">
                            <iframe id="docPreviewFrame" src="" width="100%" height="100%" frameborder="0" style="border: none;"></iframe>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" id="deleteDocBtn">Delete</button>
                    <button type="button" class="btn btn-secondary" data-mdb-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script type="text/javascript" src="../assets/MDB5-STANDARD-UI-KIT/js/mdb.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Logout confirmation script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener to logout button
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show SweetAlert confirmation
                    Swal.fire({
                        title: 'Logout Confirmation',
                        text: 'Are you sure you want to logout?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, logout',
                        cancelButtonText: 'No, cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // If confirmed, redirect to logout page
                            window.location.href = 'logout.php';
                        }
                    });
                });
            }
        });
    </script>

    <!-- Initialize modals -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all modals
            const modalElements = document.querySelectorAll('.modal');
            modalElements.forEach(function(modalEl) {
                new mdb.Modal(modalEl);
            });

            // Function to get URL parameter
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // Function to fetch employee data from server
            function fetchEmployeeDataFromServer(employeeId) {
                return new Promise((resolve, reject) => {
                    // Show loading indicator
                    document.getElementById('employee-profile').innerHTML = '<div class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-3">Loading employee data...</p></div>';

                    // Fetch employee data from server
                    fetch('manage_employee.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            getEmployee: true,
                            tbl_emp_id: employeeId,
                        }),
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.error) {
                            reject(new Error(data.error));
                        } else {
                            // Store the employee data in localStorage
                            localStorage.setItem('employee_data', JSON.stringify(data));
                            window.employeeData = data;
                            resolve(data);
                        }
                    })
                    .catch(error => {
                        reject(error);
                    });
                });
            }

            // Try to load employee data with retry mechanism
            async function loadEmployeeData() {
                // First try to get from localStorage
                let employeeData = null;
                try {
                    const storedData = localStorage.getItem("employee_data");
                    if (storedData) {
                        employeeData = JSON.parse(storedData);
                        window.employeeData = employeeData;
                        console.log("Employee data loaded from localStorage:", employeeData);
                    }
                } catch (error) {
                    console.error("Error parsing employee data from localStorage:", error);
                }

                // If no data in localStorage or data is incomplete, try to get from URL parameter
                if (!employeeData || !employeeData.tbl_emp_id) {
                    const employeeId = getUrlParameter('employee_id');
                    if (employeeId) {
                        try {
                            console.log("Fetching employee data from server for ID:", employeeId);
                            await fetchEmployeeDataFromServer(employeeId);
                            // Reload the page to ensure all components initialize properly
                            // window.location.reload(); // Uncomment if needed
                        } catch (error) {
                            console.error("Error fetching employee data from server:", error);
                            document.getElementById('employee-profile').innerHTML = `
                                <div class="alert alert-danger" role="alert">
                                    <h4 class="alert-heading">Error Loading Employee Data</h4>
                                    <p>There was a problem loading the employee information. Please try again.</p>
                                    <hr>
                                    <p class="mb-0">Error details: ${error.message}</p>
                                    <button class="btn btn-primary mt-3" onclick="window.location.reload()">Retry</button>
                                </div>
                            `;
                        }
                    } else {
                        console.error("No employee ID available in URL or localStorage");
                        document.getElementById('employee-profile').innerHTML = `
                            <div class="alert alert-warning" role="alert">
                                <h4 class="alert-heading">No Employee Selected</h4>
                                <p>Please return to the employee list and select an employee to view.</p>
                                <a href="manage_employee.php" class="btn btn-primary mt-3">Go to Employee List</a>
                            </div>
                        `;
                    }
                }
            }

            // Load employee data when page loads
            loadEmployeeData();
        });
    </script>

    <?php include '../assets/js/employee_profile.php'; ?>
    <!-- Daily Journal scripts commented out
    <script src="../assets/js/daily_journal_new.js"></script>
    -->
    <script src="../assets/js/journal_documents.js"></script>
    <script src="../assets/js/profile_journal_manager.js"></script>
</body>

</html>