    <?php
    // sidebar.php

    $currentPage = basename($_SERVER['PHP_SELF']);
    ?>
    <div class="swipe-indicator" title="Swipe right to show sidebar"></div>
    <div class="sidebar card" id="sidebar">
        <div class="sidebar-header d-flex justify-content-start ">
            <h1 style="font-size: 40px; font-family: '<PERSON>ush<PERSON>', cursive; color: #4F4F4F;">DICT SDN</h1>
            <img src="../assets/img/dict_logo_icon.png" alt="DICT Logo" class="dict-logo-icon">
        </div>
        <div class="sidebar-menu mt-5 ">
            <ul>
                <li class="<?php echo ($currentPage == 'dashboard.php') ? 'active' : ''; ?>">
                    <a href="dashboard.php"><i class="fas fa-home"></i><span>Dashboard</span></a>
                </li>

                <li class="<?php echo ($currentPage == 'manage_employee.php') ? 'active' : ''; ?>">
                    <a href="manage_employee.php"><i class="fas fa-user"></i><span>Manage Employee</span></a>
                </li>
                <li class="<?php echo ($currentPage == 'manage_supervisor.php') ? 'active' : ''; ?>">
                    <a href="manage_supervisor.php"><i class="fas fa-user-tie"></i><span>Manage Supervisor</span></a>
                </li>
                <li class="<?php echo ($currentPage == 'manage_attendance.php') ? 'active' : ''; ?>">
                    <a href="manage_attendance.php"><i class="fas fa-clock"></i><span>Manage Attendance</span></a>
                </li>
                <li class="<?php echo ($currentPage == 'manage_time_ranges.php') ? 'active' : ''; ?>">
                    <a href="manage_time_ranges.php"><i class="fas fa-calendar-week"></i><span>Manage Time-Range</span></a>
                </li>
                <li class="<?php echo ($currentPage == 'manage_holidays.php') ? 'active' : ''; ?>">
                    <a href="manage_holidays.php"><i class="fas fa-calendar-alt"></i><span>Manage Holidays</span></a>
                </li>
                <li class="<?php echo ($currentPage == 'export_database.php') ? 'active' : ''; ?>">
                    <a href="export_database.php"><i class="fas fa-database"></i><span>Database</span></a>
                </li>
                <!-- <li class="<?php echo ($currentPage == 'test_auto_absent.php') ? 'active' : ''; ?>">
                    <a href="test_auto_absent.php"><i class="fas fa-user-clock"></i><span>Auto-Absent System</span></a>
                </li> -->
                <!-- <li class="<?php echo ($currentPage == 'generate_dtr.php') ? 'active' : ''; ?>">
                    <a href="generate_dtr.php"><i class="fas fa-file-alt"></i><span>Daily Time Record</span></a>
                </li> -->

            </ul>
        </div>
        <div class="logout-button">
            <a href="#" id="logoutButton"><i class="fas fa-power-off"></i><span>Logout</span></a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.getElementById('logoutButton').addEventListener('click', function(event) {
            event.preventDefault(); // Prevent the default link behavior

            Swal.fire({
                title: 'Are you sure?',
                text: "You will be logged out of the system.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, logout!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Redirect to the logout script to destroy the session
                    window.location.href = 'logout.php';
                }
            });
        });
    </script>